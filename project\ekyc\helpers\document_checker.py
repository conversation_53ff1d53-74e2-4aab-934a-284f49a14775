from pydash import get, set_
from .get_form import get_form_answer, get_form_settings
from datetime import datetime
import re
import hashlib


def get_schema_conditions(schema):
    return {
        "face": {
            "value": get(schema, ["configs", "ignore_face"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": None,
        },
        "ocr": {
            "value": get(schema, ["configs", "ignore_ocr"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": None,
        },
        "supported_country": {
            "value": get(schema, ["configs", "ignore_supported_country"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": None,
        },
        "validity": {
            "value": get(schema, ["configs", "ignore_validity"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": None,
        },
        "orientation": {
            "value": get(schema, ["configs", "ignore_orientation"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": None,
        },
        "mrz": {
            "value": get(schema, ["configs", "ignore_mrz"], False),
            "method": "ignore",
            "include_document_types": ["passport"],
            "exclude_document_types": None,
        },
        "mrz_expiry": {
            "value": get(schema, ["configs", "ignore_mrz_expiry"], False),
            "method": "ignore",
            "include_document_types": ["passport"],
            "exclude_document_types": None,
        },
        "image_quality": {
            "value": get(schema, ["configs", "ignore_image_quality"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": None,
        },
        "warning": {
            "value": get(schema, ["configs", "check_warning"], False),
            "method": "check",
            "include_document_types": None,
            "include_document_types": None,
        },
        # Below logics are calculated our side
        "expiry": {
            "value": get(schema, ["configs", "check_expiry"], False),
            "method": "check",
            "include_document_types": None,
            "exclude_document_types": ["white_card", "border_pass", "monk_card", "immigration_card", "other_document"],
        },
        "expiry_not_found": {
            "value": get(schema, ["configs", "ignore_expiry_not_found"], False),
            "method": "ignore",
            "include_document_types": None,
            "exclude_document_types": ["white_card", "border_pass", "monk_card", "immigration_card", "other_document"],
        },
        "id_match_with": {
            "value": get(schema, ["configs", "check_id_match_with"], False),
            "method": "check",
            "include_document_types": None,
            "exclude_document_types": ["white_card", "border_pass", "monk_card", "immigration_card", "other_document"],
        },
        "ocr_fields": {
            "value": get(schema, ["configs", "check_ocr_fields"], False),
            "method": "check",
            "include_document_types": None,
            "exclude_document_types": ["white_card", "border_pass", "monk_card", "immigration_card", "other_document"],
        },
        "age": {
            "value": get(schema, ["configs", "check_age"], False),
            "method": "check",
            "include_document_types": None,
            "exclude_document_types": ["white_card", "border_pass", "monk_card", "immigration_card", "other_document"],
        },
        "hashed_document_match_with": {
            "value": get(schema, ["configs", "check_hashed_document_match_with"], False),
            "method": "check",
            "include_document_types": None,
            "exclude_document_types": ["white_card", "border_pass", "monk_card", "immigration_card", "other_document"],
        },
    }


def check_condition_pass(condition, result_value, document_type=None):
    condition_value = condition["value"]
    method = condition["method"]

    include_document_types = condition.get("include_document_types", [])
    if include_document_types and (document_type not in include_document_types):
        return True

    exclude_document_types = condition.get("exclude_document_types", [])
    if exclude_document_types and (document_type in exclude_document_types):
        return True

    if method == "ignore":
        if condition_value:
            return True
        else:
            return result_value
    if method == "check":
        if condition_value:
            return result_value
        else:
            return True
    return False


def check_by_schema_conditions(schema, response, with_ocr=False, ocr_result={}, applied_form=None):
    pass_obj: dict[str, bool] = {}
    log_obj: dict[str, bool] = {}

    conditions = get_schema_conditions(schema)
    current_document_type = get(response, ["document_type"], None)

    # Additional check
    if with_ocr:
        check_ocr_fields = conditions["ocr_fields"]["value"]
        if check_ocr_fields:
            pass_obj["ocr_fields"] = True
            log = {"result": {}}
            for entry in check_ocr_fields:
                # Format string -> dict
                if isinstance(entry, str):
                    entry = {"field": entry}

                field = entry.get("field")
                params = entry.get("params", {})
                skip = False

                # Check if correct document type
                params_document_type = params.get("document_type", None)
                if not params_document_type:
                    # Deprecated key
                    params_document_type = entry.get("document_type", None)

                if params_document_type and params_document_type != current_document_type:
                    skip = True

                if not field:
                    skip = True

                # Check if correct flag
                flag_name = params.get("flag_name", None)
                flag_equal = params.get("flag_equal", None)
                flag_value = get(ocr_result, [flag_name], None)
                if flag_name and flag_value != flag_equal:
                    skip = True

                # Check field
                pass_check = True
                if not skip:
                    value = ocr_result.get(field)
                    empty = value == "" or value == None
                    if empty:
                        pass_obj["ocr_fields"] = False
                        pass_check = False

                log["result"][field] = {"pass": pass_check, "skip": skip}
            log_obj["ocr_fields"] = log

        check_expiry = conditions["expiry"]["value"]
        if check_expiry:
            pass_obj["expiry"] = True
            expire_date_raw: str = get(ocr_result, ["date_of_expiry"])
            is_lifelong: bool = get(ocr_result, ["date_of_expiry_is_lifelong"], False)
            expire_date: datetime | None = None
            now_date: datetime | None = None

            if is_lifelong:
                pass_obj["expiry"] = True
            elif not expire_date_raw:
                pass_obj["expiry_not_found"] = False
                pass_obj["expiry"] = False
            elif expire_date_raw:
                expire_date = datetime.strptime(expire_date_raw, "%Y-%m-%d")
                now_date = datetime.now()
                pass_obj["expiry"] = expire_date > now_date

            log_obj["expiry"] = {
                "expire_date_raw": expire_date_raw,
                "is_lifelong": is_lifelong,
                "expire_date": expire_date.isoformat() if expire_date else None,
                "now_date": now_date.isoformat() if now_date else None,
            }

        check_id_match_with = conditions["id_match_with"]["value"]
        if check_id_match_with:
            pass_obj["id_match_with"] = True
            nid = get(ocr_result, ["nid"])
            pattern: str = get_form_answer(applied_form, check_id_match_with)
            re_str: str | None = None
            if isinstance(pattern, str) and nid:
                re_str = re.sub(r"[xX]", ".", pattern)
                regex = re.compile(re_str)
                pass_obj["id_match_with"] = bool(regex.match(nid))
            else:
                pass_obj["id_match_with"] = False

            log_obj["id_match_with"] = {"nid": nid, "pattern": pattern, "re_str": re_str}

        check_age = conditions["age"]["value"]
        if check_age:
            pass_obj["age"] = True
            age_field = get(check_age, ["field"], "age")
            age_value = get(ocr_result, [age_field], None)
            min_age = get(check_age, ["min"], None)
            max_age = get(check_age, ["max"], None)
            if min_age is not None:
                if age_value is None or age_value < min_age:
                    pass_obj["age"] = False
            if max_age is not None:
                if age_value is None or age_value > max_age:
                    pass_obj["age"] = False

            log_obj["age"] = {"age_field": age_field, "age_value": age_value, "min_age": min_age, "max_age": max_age}

        check_warning = conditions["warning"]["value"]
        if check_warning:
            pass_obj["warning"] = True
            is_list = isinstance(check_warning, list)
            warning = get(response, "warning", {})
            if is_list:
                fields = check_warning
            else:
                fields = warning.keys()
            pass_obj["warning"] = all(get(warning, [field, "pass"], False) == True for field in fields)

        check_hashed_document_match_with = conditions["hashed_document_match_with"]["value"]
        if check_hashed_document_match_with:
            pass_obj["hashed_document_match_with"] = True

            # Gather hashed document number and ocr document number
            target_hashed_document_number: str = get_form_answer(applied_form, check_hashed_document_match_with)
            ocr_document_number = get(ocr_result, "document_number", "")

            # Add salt and template together
            salt = get_form_settings(applied_form, "ekyc.front_card.document_hash_salt", "")
            salt_template = get_form_settings(
                applied_form, "ekyc.front_card.document_hash_salt_template", "{{salt}}{{document_number}}"
            )
            input_string = salt_template.replace("{{document_number}}", ocr_document_number)
            input_string = input_string.replace("{{salt}}", salt)

            # Hash the salted string
            salted_string = input_string.encode("utf-8")
            hash_object = hashlib.sha256(salted_string)
            hash_ocr_document_hex_dig = hash_object.hexdigest()

            if target_hashed_document_number is None or target_hashed_document_number == "":
                pass_obj["hashed_document_match_with"] = True
            elif isinstance(target_hashed_document_number, str) and target_hashed_document_number:
                pass_obj["hashed_document_match_with"] = (
                    hash_ocr_document_hex_dig == target_hashed_document_number.lower()
                )
            else:
                pass_obj["hashed_document_match_with"] = False

            log_obj["hashed_document_match_with"] = {
                "hash_ocr_document_hex_dig": hash_ocr_document_hex_dig,
                "target_hashed_document_number": target_hashed_document_number,
            }

    # Response check
    for key, condition in conditions.items():
        if key in pass_obj:
            result = pass_obj[key]
        else:
            result = get(response, ["data", "result", key, "status"], None)

        if result != None:
            pass_obj[key] = check_condition_pass(
                condition=condition,
                result_value=result,
                document_type=current_document_type,
            )

    for key, is_pass in pass_obj.items():
        set_(response, ["data", "result", key, "status"], is_pass)
    set_(response, ["result_log"], log_obj)

    pass_all_checks = all(pass_obj.values())

    return pass_all_checks, pass_obj, log_obj

from unittest.mock import patch
from datetime import datetime, timedelta
from ..dynamicform import Dynamicform as EkycDynamicForm
from ..dynamicform.Items import *
from ..helpers.external_api import *
from ekyc.tests.mock_api import *
from .utils import AppliedFormMock
from .utils_form import EkycFormTests

EKYC_FRONT_CARD_ITEM_TYPE = "Ekyc.FrontCard"
EKYC_GET_APPLIED_FORM_PATH = "ekyc.views.base_document.get_applied_form"


class EkycTests(EkycFormTests):
    def setUp(self):
        patcher_attempt = patch(f"ekyc.models.ekyc.settings.EKYC_FRONT_CARD_MAX_ATTEMPT", 5)
        self.mock_attempt = patcher_attempt.start()
        self.addCleanup(patcher_attempt.stop)

    def test_front_card_check_expiry(self):
        """
        Should compute checkers from form schema correctly
        """

        form = AppliedFormMock(
            {
                "ekyc_frontcard": {
                    "name": "ekyc_frontcard",
                    "type": EKYC_FRONT_CARD_ITEM_TYPE,
                    "configs": {
                        "ignore_face": False,
                        "ignore_ocr": False,
                        "check_expiry": True,
                        "check_id_match_with": False,
                        "check_ocr_fields": False,
                        "check_age": False,
                        "check_warning": False,
                    },
                }
            }
        )

        patcher_get_applied_form = patch(EKYC_GET_APPLIED_FORM_PATH, lambda slug: form)
        patcher_get_applied_form.start()
        self.addCleanup(patcher_get_applied_form.stop)

        def check_expiry(ocr_result, expect_expiry=True, expect_not_found=True):
            patcher_upload_front_card = patch(
                "ekyc.views.documents.upload_front_card",
                build_mock_upload_document_function("front_card", {"ocr": ocr_result}),
            )
            patcher_upload_front_card.start()
            response = self.upload_front_success(form.slug)
            response = response.json()
            answers = EkycDynamicForm.get_answers(form, self.questions)

            if expect_expiry == True:
                self.assertTrue(response["data"]["result"]["expiry"]["status"])
                self.assertIn("ekyc_frontcard", answers)
            else:
                self.assertFalse(response["data"]["result"]["expiry"]["status"])
                self.assertNotIn("ekyc_frontcard", answers)

            if expect_not_found == True:
                self.assertNotIn("expiry_not_found", response["data"]["result"])
            else:
                self.assertFalse(response["data"]["result"]["expiry_not_found"]["status"])

            patcher_upload_front_card.stop()

        data_format = "%d %b. %Y"
        # Fail (empty)
        check_expiry(
            {},
            expect_expiry=False,
            expect_not_found=False,
        )
        # Fail
        check_expiry(
            {"date_of_expiry_en": datetime.strftime(datetime.now() + timedelta(days=-2), data_format)},
            expect_expiry=False,
            expect_not_found=True,
        )
        # Success
        check_expiry(
            {"date_of_expiry_en": datetime.strftime(datetime.now() + timedelta(days=2), data_format)},
            expect_expiry=True,
            expect_not_found=True,
        )
        # Success (life long only)
        check_expiry(
            {
                "date_of_expiry_is_lifelong": True,
            },
            expect_expiry=True,
            expect_not_found=True,
        )
        # Success (life long)
        check_expiry(
            {
                "date_of_expiry_en": datetime.strftime(datetime.now() + timedelta(days=-2), data_format),
                "date_of_expiry_is_lifelong": True,
            },
            expect_expiry=True,
            expect_not_found=True,
        )

    @patch(
        "ekyc.helpers.document_checker.get_form_answer",
        lambda applied_form, slug: "123....890123",
    )
    def test_front_card_check_id_match_with(self):
        """
        Should compute checkers from form schema correctly
        """

        form = AppliedFormMock(
            {
                "ekyc_frontcard": {
                    "name": "ekyc_frontcard",
                    "type": EKYC_FRONT_CARD_ITEM_TYPE,
                    "configs": {
                        "ignore_face": False,
                        "ignore_ocr": False,
                        "check_expiry": False,
                        "check_id_match_with": "national_id_true",
                        "check_ocr_fields": False,
                        "check_age": False,
                        "check_warning": False,
                    },
                }
            }
        )

        patcher_get_applied_form = patch(EKYC_GET_APPLIED_FORM_PATH, lambda slug: form)
        patcher_get_applied_form.start()
        self.addCleanup(patcher_get_applied_form.stop)

        def check_id_match_with(ocr_result, expect=True):
            patcher_upload_front_card = patch(
                "ekyc.views.documents.upload_front_card",
                build_mock_upload_document_function("front_card", {"ocr": ocr_result}),
            )
            patcher_upload_front_card.start()
            response = self.upload_front_success(form.slug)
            response = response.json()
            answers = EkycDynamicForm.get_answers(form, self.questions)

            if expect == True:
                self.assertTrue(response["data"]["result"]["id_match_with"]["status"])
                self.assertIn("ekyc_frontcard", answers)
            else:
                self.assertFalse(response["data"]["result"]["id_match_with"]["status"])
                self.assertNotIn("ekyc_frontcard", answers)

            patcher_upload_front_card.stop()

        # Fail
        check_id_match_with({"nid": "3210987654321"}, False)
        # Success
        check_id_match_with({"nid": "1234567890123"}, True)

    def test_front_card_check_ocr_fields(self):
        """
        Should compute checkers from form schema correctly
        """

        form = AppliedFormMock(
            {
                "ekyc_frontcard": {
                    "name": "ekyc_frontcard",
                    "type": EKYC_FRONT_CARD_ITEM_TYPE,
                    "configs": {
                        "ignore_face": False,
                        "ignore_ocr": False,
                        "check_expiry": False,
                        "check_id_match_with": False,
                        "check_ocr_fields": [
                            {"field": "nid", "document_type": "front_card"},
                            {
                                "field": "document_number",
                                "document_type": "passport",
                            },  # ignored
                            "first_name",  # both
                        ],
                        "check_age": False,
                        "check_warning": False,
                    },
                }
            }
        )

        patcher_get_applied_form = patch(EKYC_GET_APPLIED_FORM_PATH, lambda slug: form)
        patcher_get_applied_form.start()
        self.addCleanup(patcher_get_applied_form.stop)

        def check_ocr_fields(ocr_result, expect=True):
            patcher_upload_front_card = patch(
                "ekyc.views.documents.upload_front_card",
                build_mock_upload_document_function("front_card", {"ocr": ocr_result}),
            )
            patcher_upload_front_card.start()
            response = self.upload_front_success(form.slug)
            response = response.json()
            answers = EkycDynamicForm.get_answers(form, self.questions)

            if expect == True:
                self.assertTrue(response["data"]["result"]["ocr_fields"]["status"])
                self.assertIn("ekyc_frontcard", answers)
            else:
                self.assertFalse(response["data"]["result"]["ocr_fields"]["status"])
                self.assertNotIn("ekyc_frontcard", answers)

            patcher_upload_front_card.stop()

        # Fail (first_name not present)
        check_ocr_fields({"nid": "3210987654321", "last_name": "YEAH"}, False)
        # Fail (nid empty)
        check_ocr_fields({"nid": "", "first_name": "aaa"}, False)
        # Fail (wrong doc)
        check_ocr_fields({"document_number": "1234567890123", "first_name": "YO"}, False)

        # Success
        check_ocr_fields({"nid": "1234567890123", "first_name": "YO"}, True)

    @patch("ekyc.helpers.document_checker.get_form_answer", lambda applied_form, slug: "")
    def test_front_card_check_age(self):
        form = AppliedFormMock(
            {
                "ekyc_frontcard": {
                    "name": "ekyc_frontcard",
                    "type": EKYC_FRONT_CARD_ITEM_TYPE,
                    "configs": {
                        "ignore_face": False,
                        "ignore_ocr": False,
                        "check_expiry": False,
                        "check_id_match_with": False,
                        "check_ocr_fields": False,
                        "check_age": {"min": 18},
                        "check_warning": False,
                    },
                }
            }
        )

        patcher_get_applied_form = patch(EKYC_GET_APPLIED_FORM_PATH, lambda slug: form)
        patcher_get_applied_form.start()
        self.addCleanup(patcher_get_applied_form.stop)

        def check_age(ocr_result, expect=True):
            patcher_upload_front_card = patch(
                "ekyc.views.documents.upload_front_card",
                build_mock_upload_document_function("front_card", {"ocr": ocr_result}),
            )
            patcher_upload_front_card.start()
            response = self.upload_front_success(form.slug)
            response = response.json()
            answers = EkycDynamicForm.get_answers(form, self.questions)

            if expect == True:
                self.assertTrue(response["data"]["result"]["age"]["status"])
                self.assertIn("ekyc_frontcard", answers)
            else:
                self.assertFalse(response["data"]["result"]["age"]["status"])
                self.assertNotIn("ekyc_frontcard", answers)

            patcher_upload_front_card.stop()

        # Fail
        check_age({"age": 17}, False)
        # Success
        check_age({"age": 18}, True)
        check_age({"age": 19}, True)

    def test_front_card_check_warning_all(self):
        form = AppliedFormMock(
            {
                "ekyc_frontcard": {
                    "name": "ekyc_frontcard",
                    "type": EKYC_FRONT_CARD_ITEM_TYPE,
                    "configs": {
                        "ignore_face": False,
                        "ignore_ocr": False,
                        "check_expiry": False,
                        "check_id_match_with": False,
                        "check_ocr_fields": False,
                        "check_age": False,
                        "check_warning": True,
                    },
                }
            }
        )

        patcher_get_applied_form = patch(EKYC_GET_APPLIED_FORM_PATH, lambda slug: form)
        patcher_get_applied_form.start()
        self.addCleanup(patcher_get_applied_form.stop)

        def check(warning, expect=True):
            patcher_upload_front_card = patch(
                "ekyc.views.documents.upload_front_card",
                build_mock_upload_document_function("front_card", {"warning": warning}),
            )
            patcher_upload_front_card.start()
            response = self.upload_front_success(form.slug)
            response = response.json()
            answers = EkycDynamicForm.get_answers(form, self.questions)

            if expect == True:
                self.assertTrue(response["data"]["result"]["warning"]["status"])
                self.assertIn("ekyc_frontcard", answers)
            else:
                self.assertFalse(response["data"]["result"]["warning"]["status"])
                self.assertNotIn("ekyc_frontcard", answers)

            patcher_upload_front_card.stop()

        # Fail
        check(
            {
                "portrait_detection": {
                    "pass": False,
                    "score": 1.0,
                    "status": True,
                    "message": "",
                }
            },
            False,
        )
        check(
            {
                "portrait_detection": {
                    "pass": True,
                },
                "label": {
                    "pass": False,
                },
            },
            False,
        )
        # Success
        check(
            {
                "portrait_detection": {
                    "pass": True,
                },
                "label": {
                    "pass": True,
                },
            },
            True,
        )

    @patch(
        "ekyc.helpers.document_checker.get_form_settings",
        lambda applied_form, path, default: {
            "ekyc.front_card.document_hash_salt": "12341234",
            "ekyc.front_card.document_hash_salt_template": "{{salt}}-{{document_number}}-{{salt}}",
        }[path],
    )
    def test_front_card_check_hashed_document_match_with(self):
        """
        Should compute checkers from form schema correctly
        """

        form = AppliedFormMock(
            {
                "ekyc_frontcard": {
                    "name": "ekyc_frontcard",
                    "type": EKYC_FRONT_CARD_ITEM_TYPE,
                    "configs": {
                        "ignore_face": False,
                        "ignore_ocr": False,
                        "check_expiry": False,
                        "check_hashed_document_match_with": "hashed_document_answer",
                        "check_ocr_fields": False,
                        "check_age": False,
                        "check_warning": False,
                    },
                }
            }
        )

        patcher_get_applied_form = patch(EKYC_GET_APPLIED_FORM_PATH, lambda slug: form)
        patcher_get_applied_form.start()
        self.addCleanup(patcher_get_applied_form.stop)

        def check_hashed_document_match_with(ocr_result, mock_hashed_document_answer, expect=True):
            patcher_get_form_answer = patch(
                "ekyc.helpers.document_checker.get_form_answer",
                lambda applied_form, slug: mock_hashed_document_answer,
            )
            patcher_upload_front_card = patch(
                "ekyc.views.documents.upload_front_card",
                build_mock_upload_document_function("front_card", {"ocr": ocr_result}),
            )
            patcher_get_form_answer.start()
            patcher_upload_front_card.start()
            response = self.upload_front_success(form.slug)
            response = response.json()
            answers = EkycDynamicForm.get_answers(form, self.questions)

            if expect == True:
                self.assertTrue(response["data"]["result"]["hashed_document_match_with"]["status"])
                self.assertIn("ekyc_frontcard", answers)
            else:
                self.assertFalse(response["data"]["result"]["hashed_document_match_with"]["status"])
                self.assertNotIn("ekyc_frontcard", answers)

            patcher_get_form_answer.stop()
            patcher_upload_front_card.stop()

        # Fail Invalid hash
        check_hashed_document_match_with({"document_number": "1234567890123"}, "abcdefg1234567890", False)

        # Fail no ocr result
        check_hashed_document_match_with({}, "abcdefg1234567890", False)

        # Success with not check - target hash answer is None
        check_hashed_document_match_with({}, None, True)

        # Success with not check - target hash answer is empty string
        check_hashed_document_match_with({}, "", True)

        # Success
        check_hashed_document_match_with(
            {"document_number": "1234567890123"},
            "14E7C65ACC36D2E4ECADFC3EA5A9122AE7CD0362D942DF677E94677FEDA4CE98",
            True,
        )

"""project URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

# from django.conf.urls import url
from django.conf import settings
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.http import JsonResponse
from django.urls import include, path, re_path

from app.template import TemplateViewWithCsrf

app_name = "dynamic_form"
urlpatterns = []
if settings.ENABLE_ADMIN:
    if settings.ENABLE_ACCESS_ADMIN_PATH:
        urlpatterns += [
            re_path(r"^admin/", admin.site.urls),
        ]
    urlpatterns += i18n_patterns(
        re_path(r"^(../)?dashboard.*?$", TemplateViewWithCsrf.as_view(template_name="frontend/dashboard.html"))
    )
    if settings.ENABLE_SILKY:
        urlpatterns += [re_path(r"silk/", include("silk.urls", namespace="silk"))]

api_urls = [
    path("ekyc/", include("ekyc.urls")),
    path("style/", include("style.urls")),
    path("appsetting/", include("appsetting.urls")),
    path("bankstatement/", include("bankstatement.urls")),
    path("smartuploader/", include("smartuploader.urls")),
    path("auth/", include("app.auth.urls.auth")),
    path("accounts/", include("app.auth.urls.account")),
    path("roles/", include("app.roles.urls")),
    path("otp/", include("otp.urls")),
    path("health/", include("health_check.urls")),
    path("services/", include("services.urls")),
    path("me/", include("me.urls")),
    path("workspaces/", include("workspace.urls.workspace")),
    path("sessions/", include("app.urls.session")),
    path("information/", include("information.urls")),
    path("pricing/", include("credit_system.urls.pricing")),
    path("credit/", include("credit_system.urls.credit_system")),
    path("commands/", include("app.urls.command")),
    path("decision-flow-pipelines/", include("dynamicform.submodules.decisionflow.urls")),
    path("complyadvantage/", include("comply_advantage.urls")),
    # path('asiaverify/', include('asiaverify.urls')),
]

urlpatterns += [
    path("api/", include(api_urls)),
    path("social/", include("social_django.urls", namespace="social")),
    path("robots.txt", TemplateViewWithCsrf.as_view(template_name="client/robots.txt", content_type="text/plain")),
    *static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT),
]

urlpatterns += i18n_patterns(
    path("api/forms/", include("dynamicform.urls.form")),
    path("api/workspaces/", include("workspace.urls.workspace_form")),
    path("api/applications/", include("dynamicform.urls.application")),
    path("api/applications-status/", include("dynamicform.urls.application-status")),
    path("api/pages/", include("dynamicform.urls.page")),
    re_path(r"^(?!(?:admin|api|forms|social)).*$", TemplateViewWithCsrf.as_view(template_name="frontend/client.html")),
)


def default_json_error_400(request, exception=None):
    return JsonResponse(
        {
            "error": {
                "status_code": 400,
                "message": "Bad Request",
                "detail": "Bad request syntax or unsupported method",
            }
        },
        status=400,
    )


def default_json_error_403(request, exception=None):
    return JsonResponse(
        {
            "error": {
                "status_code": 403,
                "message": "Forbidden",
                "detail": "Request forbidden -- authorization will not help",
            }
        },
        status=403,
    )


def default_json_error_404(request, exception=None):
    return JsonResponse(
        {
            "error": {
                "status_code": 404,
                "message": "Not Found",
                "detail": "Nothing matches the given URI",
            }
        },
        status=404,
    )


def default_json_error_500(request, exception=None):
    return JsonResponse(
        {
            "error": {
                "status_code": 500,
                "message": "Internal Server Error",
                "detail": "Server got itself in trouble",
            }
        },
        status=500,
    )


handler400 = default_json_error_400
handler403 = default_json_error_403
handler404 = default_json_error_404
handler500 = default_json_error_500

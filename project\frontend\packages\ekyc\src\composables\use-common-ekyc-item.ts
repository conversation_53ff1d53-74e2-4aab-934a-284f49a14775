import { type MaybeRef, promiseTimeout } from '@vueuse/core';
import forEach from 'lodash/forEach';
import get from 'lodash/get';
import mapValues from 'lodash/mapValues';
import merge from 'lodash/merge';
import set from 'lodash/set';
import { storeToRefs } from 'pinia';
import type { ExtractPropTypes, Ref } from 'vue';
import { useI18n } from 'vue-i18n-composable';

import { checkBrowser, isPocketDevice } from '@helpers/helpers/user-agent';

import { sendEkycLog, sendLivenessCancel, sendLivenessFail, sendMedia } from '@ekyc/services/ekyc';
import { useEkycStore } from '@ekyc/store/modules/ekyc';

import { useEkycSettings } from './use-ekyc-settings';
import { usePopup } from './use-popup';

const BaseProps = {
  setting: {
    type: Object as () => Partial<Types.BaseEkycComponentSetting>,
    default: () => ({}) as Partial<Types.BaseEkycComponentSetting>,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  allowDebugMode: {
    type: Boolean,
    default: false,
  },
};

export const LivenessProps = {
  ...BaseProps,
  setting: {
    type: Object as () => Partial<Types.LivenessComponentSetting>,
    default: () => ({}) as Partial<Types.LivenessComponentSetting>,
  },
};

export const DocumentProps = {
  ...BaseProps,
  setting: {
    type: Object as () => Partial<Types.DocumentComponentSetting>,
    default: () => ({}) as Partial<Types.DocumentComponentSetting>,
  },
  selectedCountry: {
    type: String,
    required: true,
  },
};

export const useCommonEkycItem = <
  MT extends MaybeRef<Types.EkycMediaTypes>,
  IT extends MaybeRef<Types.EkycItemTypes>,
>({
  media,
  itemType,
  selectedCountry = '',
}: {
  media: MT;
  itemType: IT;
  selectedCountry?: string | Ref<string>;
}) => {
  const context = getCurrentInstance();
  const props = context?.proxy.$props as ExtractPropTypes<typeof BaseProps>;
  // Refs
  const recorder = ref<any>();
  // Composes
  const { allSettings, defaultSettings, resetSettings } = useEkycSettings();
  const {
    showPopupIsDesktop,
    showPopupWebview,
    showPopupCameraPermission,
    showPopupGoToSetting,
    overridePopupGoToSettingContent,
    shouldPopupIsDesktopHidden,
    onPopupTryToOpenCamera,
  } = usePopup();
  const { locale } = useI18n();
  const ekycStore = useEkycStore();
  const { resultState } = storeToRefs(ekycStore);

  // Data
  const isDisplayMediaRecorder = ref(false);
  const isCaptureFlipped = ref(false);
  const isUploading = ref(false);

  // Computed
  const mediaData = computed(() => ekycStore.getMediaData(unref(media)));

  // Methods
  function emitUploaded(res: Types.EkycDocumentApiResult) {
    context.proxy.$emit('uploaded', { ...res, media: unref(media) });
    closeVideoCamera();
  }

  function emitFail(res: Types.EkycRecordFailPayload) {
    context.proxy.$emit('fail', { ...res, media: unref(media) });
    closeVideoCamera();
  }

  function emitError(res: Types.EkycRecordErrorPayload) {
    context.proxy.$emit('error', { ...res, media: unref(media) });
    closeVideoCamera();
  }

  function setMediaData(data: Types.RecordedData[]) {
    const blobs: Blob[] = data.map(d => d.blob);
    const urls: string[] = data.map(d => d.url);
    const filenames: (string | undefined)[] = data.map(d => d.filename);
    ekycStore.setBlob(unref(media), { blobs, urls, filenames });
  }

  // hooks
  const beforeCameraRequestCallbacks: (() => Promise<boolean>)[] = [];
  const uploadedCallbacks: ((res: any) => void)[] = [emitUploaded];

  // eslint-disable-next-line no-use-before-define, @typescript-eslint/no-use-before-define
  onPopupTryToOpenCamera.value = checkCanOpenCamera as Function;

  function closeVideoCamera() {
    isDisplayMediaRecorder.value = false;
  }

  function checkIfPortrait(stream: MediaStream) {
    const streamSettings = stream.getVideoTracks()[0].getSettings();
    return streamSettings.height > streamSettings.width;
  }

  async function checkCanOpenCamera() {
    /* Wait for ALL PASS beforeCameraRequestCallbacks */
    const pass = (await Promise.all(beforeCameraRequestCallbacks.map(cb => cb()))).every(p => p);
    console.log('beforeCameraRequestCallbacks:', pass);
    if (!pass) {
      return false;
    }

    /* Pre-request camera to know if has permission before recorder open */
    let canOpenCamera = false;
    context.proxy.$emit('loading', true);
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
      stream.getTracks().forEach(track => track.stop());
      canOpenCamera = true;
    } catch (error) {
      console.error(error);
      const errorBody = {
        action: 'error',
        media: unref(media),
        code: 'error_permission_denied',
        error: 'ekyc.recorder.error_permission_denied',
        detail: error.toString(),
      } as const;
      emitError(errorBody);
      sendEkycLog(errorBody).catch(() => {});
    }
    console.log('canOpenCamera:', canOpenCamera);
    if (canOpenCamera) {
      isDisplayMediaRecorder.value = true;
    } else {
      isDisplayMediaRecorder.value = false;

      const { badBrowser } = checkBrowser();
      const shouldShowPopupWebview = allSettings[unref(media)].show_popup_webview;
      const shouldShowPopupCameraPermission =
        allSettings[unref(media)].show_popup_camera_permission;
      const shouldShowPopupGoToSetting = allSettings[unref(media)].show_popup_go_to_setting;
      const shouldOverridePopupGoToSetting =
        !!allSettings[unref(media)].override_popup_go_to_setting.content;
      console.log('badBrowser', badBrowser);
      console.log('shouldShowPopupWebview', shouldShowPopupWebview);
      if (shouldShowPopupGoToSetting) {
        showPopupGoToSetting.value = true;
      } else if (badBrowser && shouldShowPopupWebview) {
        showPopupWebview.value = shouldShowPopupWebview;
      } else {
        showPopupCameraPermission.value = shouldShowPopupCameraPermission;
      }
      if (shouldOverridePopupGoToSetting) {
        overridePopupGoToSettingContent.value =
          allSettings[unref(media)].override_popup_go_to_setting.content;
      }
    }
    context.proxy.$emit('loading', false);
    return canOpenCamera;
  }

  function checkOpenPopupDesktop() {
    const isDesktop = !isPocketDevice();
    if (
      isDesktop &&
      !shouldPopupIsDesktopHidden.value &&
      allSettings[unref(media)].show_popup_desktop &&
      !allSettings[unref(media)].dummy
    ) {
      showPopupIsDesktop.value = true;
      // shouldPopupIsDesktopHidden.value = true; // Uncomment this line to show only once
    }
    console.log('checkOpenPopupDesktop:', showPopupIsDesktop.value);
    return !showPopupIsDesktop.value;
  }

  async function openVideoCamera() {
    if (!checkOpenPopupDesktop()) {
      return false;
    }

    return checkCanOpenCamera();
  }

  async function upload({ logs = [] }) {
    try {
      if (!mediaData.value.urls?.[0]) {
        console.error('No media to upload');
        return false;
      }
      const isVerticalExperience = allSettings[unref(media)]?.configs?.enabled_vertical_experience;
      const currentMedia = unref(media);
      const apiResponse = await sendMedia({
        media: currentMedia,
        selectedCountry: unref(selectedCountry),
        isVerticalExperience,
        actionLogs: logs,
      });
      // If media not qualified
      if (!apiResponse.success) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw apiResponse;
      }

      // Check pass result
      if (unref(media) === 'liveness') {
        const livenessResponse = apiResponse.data as unknown as Types.EkycLivenessApiResult;
        livenessResponse.result = checkLivenessPassResult(livenessResponse.result);
        uploadedCallbacks.forEach(fn => fn({ ...livenessResponse, logs: apiResponse.processLogs }));
        console.log('Calling liveness uploadedCallbacks', uploadedCallbacks.length);
      } else if (apiResponse.data?.data) {
        const documentResponse = apiResponse.data as unknown as Types.EkycDocumentApiResult;
        documentResponse.data.result = checkDocumentPassResult(documentResponse.data.result);
        uploadedCallbacks.forEach(fn => fn({ ...documentResponse, logs: apiResponse.processLogs }));
        console.log('Calling document uploadedCallbacks', uploadedCallbacks.length);
      }

      return true;
    } catch (error) {
      console.error(error);
      const errorBody = {
        action: 'error',
        media: unref(media),
        code: 'error_upload',
        error,
        from: 'server',
      } as const;
      emitError(errorBody);

      const errorData = get(error, 'response.data', error);
      // const errorType = get(errorData, 'error_type', 'unknown_server_error');
      const dataStr = typeof errorData === 'object' ? JSON.stringify(errorData) : errorData;
      sendEkycLog({ ...errorBody, error: dataStr }).catch(() => {});

      return false;
    }
  }

  async function onRecordSuccess(payload: Types.EkycRecordFinishedPayload) {
    if (isUploading.value) {
      return;
    }
    isUploading.value = true;
    setMediaData(payload.data);
    isCaptureFlipped.value = payload.flipped;
    context.proxy.$emit('captured', unref(media));
    resultState.value = 'uploading';

    await upload({
      logs: payload.logs || [],
    });

    await nextTick();
    isUploading.value = false;
  }

  async function onRecordFail(payload: Types.EkycRecordFinishedPayload) {
    if (mediaData.value.urls?.[0]) {
      resultState.value = 'already_passed';
    } else {
      resultState.value = 'failed_others';
    }
    let errorBody = {
      ...payload,
    };

    let waitFailApiPromise: Promise<void> | null = null;
    if (payload.isRecording) {
      waitFailApiPromise = (async () => {
        try {
          const res = await sendLivenessFail({
            logs: payload.logs || [],
            recordedData: payload.data,
          }).catch(e => {
            console.error(e);
            return null;
          });
          errorBody = { ...errorBody, ...res.data };
          // BIG liveness logs are sent, no need to send again
          delete errorBody.logs;
        } catch {
          //
        }
      })();
      const waitMinimumPromise = promiseTimeout(1000);
      await Promise.all([waitMinimumPromise]);
    }

    if (payload.action === 'fail') {
      emitFail(errorBody as Types.EkycRecordFailPayload);
    } else if (payload.action === 'error') {
      emitError(errorBody as Types.EkycRecordErrorPayload);
    }

    if (waitFailApiPromise !== null) {
      await waitFailApiPromise;
    }
  }

  async function onRecordClose(payload: Types.EkycRecordFinishedPayload) {
    closeVideoCamera();
    let closeBody = {
      media: unref(media),
      ...payload,
    };
    if (payload.isRecording) {
      try {
        const res = await sendLivenessCancel({
          logs: payload.logs || [],
          recordedData: payload.data,
        }).catch(e => {
          console.error(e);
          return null;
        });
        closeBody = { ...closeBody, ...res.data };
      } catch {
        //
      }
    }
    context.proxy.$emit('close', closeBody);
  }

  async function onRecordFinished(payload: Types.EkycRecordFinishedPayload) {
    context.proxy.$emit('loading', true);
    switch (payload.action) {
      case 'complete':
        await onRecordSuccess(payload);
        break;
      case 'error':
      case 'fail': {
        await onRecordFail(payload);
        break;
      }
      case 'close': {
        await onRecordClose(payload);
        break;
      }
      default:
        break;
    }
    context.proxy.$emit('loading', false);
  }

  async function externalUpload(recordedData: Types.RecordedData[]) {
    if (!recordedData) {
      console.error('Please provide data array with following format', [
        {
          blob: '/* Binary */',
          filename: '/* String */',
        },
      ]);
      return Promise.reject(new Error('no data'));
    }

    return onRecordFinished({
      action: 'complete',
      data: recordedData,
      flipped: false,
    });
  }

  async function externalUploadBase64(base64DataList: string[] = []) {
    if (!base64DataList.length) {
      console.error('Please provide base64 string array');
      return Promise.reject(new Error('no data'));
    }
    function base64toBlob(base64Data: string, contentType = '', sliceSize = 1024) {
      const byteCharacters = atob(base64Data);
      const byteArrays = [];

      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);

        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i += 1) {
          byteNumbers[i] = slice.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }

      const blob = new Blob(byteArrays, { type: contentType });
      return blob;
    }

    const recordedDataList: Types.RecordedData[] = base64DataList.map((base64Data, i) => ({
      blob: base64toBlob(base64Data, 'image/jpeg'),
      filename: `external-${i}.jpg`,
      url: '',
    }));

    return externalUpload(recordedDataList);
  }

  const stopChildPreview = () => {
    if (recorder.value?.videoRef) {
      recorder.value?.stopPreview();
    }
  };

  function checkLivenessPassResult(result: Types.EkycLivenessApiResult['result']) {
    const setting = allSettings.liveness;
    const passResult = mapValues(result, (value, key) => {
      const passed = value?.status || get(setting, ['configs', 'ignore', key], false);
      return passed;
    });

    console.log('ekyc_liveness_check', passResult);
    return passResult;
  }

  function checkDocumentPassResult(result: Types.EkycDocumentApiResult['data']['result']) {
    const passResult = result;

    passResult['both'] = {
      status:
        !('face' in passResult) ||
        passResult.face.status ||
        !('ocr' in passResult) ||
        passResult.ocr.status,
    };

    /* Face/OCR + Ignore checking */
    const setting = allSettings[unref(media)] as Types.DocumentComponentSetting;
    forEach(passResult, (val, key: Types.EkycDocumentValidationTypes) => {
      const isIgnore = get(setting, ['configs', `ignore_${key}`], false);
      const pass = val?.status === true || isIgnore;
      passResult[key].status = pass;
    });

    console.log('ekyc_document_check', passResult);
    return passResult;
  }

  // Watches

  const isInterfaceReady = ref(false);

  watch(
    () => props.setting,
    val => {
      resetSettings(unref(media));
      merge(allSettings[unref(media)], val);

      ekycStore.setLogUrl(allSettings[unref(media)].log_url);

      if (allSettings[unref(media)].expose_upload_interface && !isInterfaceReady.value) {
        set(window, ['ekyc', unref(media), 'externalUpload'], externalUploadBase64);
        if (globalThis.webkit) {
          console.log('Calling ekyc onReady for iOS webkit');
          globalThis.webkit.messageHandlers.onReady.postMessage('ready');
        } else if (globalThis.EkycInterface) {
          console.log('Calling ekyc onReady for android interface');
          globalThis.EkycInterface.onReady('ready');
        } else {
          console.log('No calling ekyc onReady');
        }
        isInterfaceReady.value = true;
      }
    },
    { immediate: true, deep: true },
  );

  onBeforeUnmount(() => {
    delete (window as any).ekyc;
  });

  provide('emitUploaded', emitUploaded);

  return {
    ...usePopup(),

    recorder,

    allSettings,
    defaultSettings,

    isDisplayMediaRecorder,
    media,
    itemType,
    isCaptureFlipped,
    mediaData,
    locale,
    selectedCountry,
    isUploading,

    upload,
    closeVideoCamera,
    openVideoCamera,
    onRecordFinished,
    onRecordFail,
    emitUploaded,
    emitFail,
    emitError,

    uploadedCallbacks,
    beforeCameraRequestCallbacks,

    stopChildPreview,
    setMediaData,
  };
};

export default { useCommonEkycItem };

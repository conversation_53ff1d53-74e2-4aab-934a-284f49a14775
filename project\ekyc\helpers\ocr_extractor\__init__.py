from typing import Dict
from .base import (
    extract as _basic_extract,
    extract_name,
    extract_title,
    extract_gender,
    extract_document_number,
    extract_laser_number,
)


def default(ocr: Dict) -> Dict:
    return ocr


def front_card(ocr: Dict) -> Dict:
    suffixes = ["native", "th"]
    ocr = extract_name(ocr, suffixes, "front_card")
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "front_card")
    ocr = _basic_extract(ocr)
    return ocr


def passport(ocr: Dict) -> Dict:
    suffixes = ["en"]
    ocr = extract_name(ocr, suffixes, "passport")
    ocr = extract_gender(ocr)  # To get gender_code before extract_title
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "passport")
    ocr = _basic_extract(ocr)
    return ocr


def driver_license(ocr: Dict) -> Dict:
    suffixes = ["native", "en"]
    ocr = extract_name(ocr, suffixes, "driver_license")
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "driver_license")
    ocr = _basic_extract(ocr)
    return ocr


def residence_permit(ocr: Dict) -> Dict:
    suffixes = ["native", "en"]
    ocr = extract_name(ocr, suffixes, "residence_permit")
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "residence_permit")
    ocr = _basic_extract(ocr)
    return ocr


def thai_alien_card(ocr: Dict) -> Dict:
    suffixes = ["native", "th"]
    ocr = extract_name(ocr, suffixes, "thai_alien_card")
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "thai_alien_card")
    ocr = _basic_extract(ocr)
    return ocr


def portrait(ocr: Dict) -> Dict:
    return ocr


def ci_passport(ocr: Dict) -> Dict:
    suffixes = ["en"]
    ocr = extract_name(ocr, suffixes, "ci_passport")
    ocr = extract_gender(ocr)
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "ci_passport")
    ocr = _basic_extract(ocr)
    return ocr


def work_permit_card(ocr: Dict) -> Dict:
    suffixes = ["native", "en"]
    ocr = extract_name(ocr, suffixes, "work_permit_card")
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "work_permit_card")
    ocr = _basic_extract(ocr)
    return ocr


def work_permit_book(ocr: Dict) -> Dict:
    suffixes = ["native", "en"]
    ocr = extract_name(ocr, suffixes, "work_permit_book")
    ocr = extract_title(ocr)
    ocr = extract_gender(ocr)
    ocr = extract_document_number(ocr, "work_permit_book")
    ocr = _basic_extract(ocr)
    return ocr


def white_card(ocr: Dict) -> Dict:
    ocr = extract_document_number(ocr, "white_card")
    return ocr


def border_pass(ocr: Dict) -> Dict:
    ocr = extract_document_number(ocr, "border_pass")
    return ocr


def monk_card(ocr: Dict) -> Dict:
    ocr = extract_document_number(ocr, "monk_card")
    return ocr


def immigration_card(ocr: Dict) -> Dict:
    ocr = extract_document_number(ocr, "immigration_card")
    return ocr


def other_document(ocr: Dict) -> Dict:
    ocr = extract_document_number(ocr, "other_document")
    return ocr


def back_card(ocr: Dict) -> Dict:
    ocr = extract_laser_number(ocr)
    return ocr


def extract_document(type: str, ocr: Dict) -> Dict:
    extractors = {
        "front_card": front_card,
        "passport": passport,
        "driver_license": driver_license,
        "residence_permit": residence_permit,
        "thai_alien_card": thai_alien_card,
        "portrait": portrait,
        "ci_passport": ci_passport,
        "work_permit_card": work_permit_card,
        "work_permit_book": work_permit_book,
        "white_card": white_card,
        "border_pass": border_pass,
        "monk_card": monk_card,
        "immigration_card": immigration_card,
        "other_document": other_document,
        "back_card": back_card,
    }
    extract_func = extractors.get(type, default)

    return extract_func(ocr)

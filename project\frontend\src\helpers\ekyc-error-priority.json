{"liveness": ["face_compare", "label", "missing_frames", "liveness_detection", "liveness_detection_face_too_small", "liveness_detection_face_not_found", "liveness_detection_too_many_faces", "liveness_detection_face_is_occluded", "liveness_detection_face_close_to_border", "liveness_detection_face_cropped", "liveness_detection_face_angle_too_large", "liveness_detection_failed_to_predict_landmarks", "liveness_detection_service_unavailable", "liveness_detection_face_too_close"], "front_card": ["both", "orientation", "face", "ocr", "supported_country", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "passport": ["ocr", "supported_country", "warning", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "driver_license": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "residence_permit": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "thai_alien_card": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "portrait": ["both", "orientation", "face", "ocr", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age", "supported_country"], "ci_passport": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "work_permit_card": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "work_permit_book": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "travel_document": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "white_card": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "border_pass": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "monk_card": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "immigration_card": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "other_document": ["ocr", "supported_country", "liveness_detection", "both", "orientation", "face", "mrz", "mrz_expiry", "image_quality", "warning", "expiry_not_found", "expiry", "id_match_with", "ocr_fields", "age"], "backcard": ["ocr", "image_quality", "warning", "supported_country", "validity"]}